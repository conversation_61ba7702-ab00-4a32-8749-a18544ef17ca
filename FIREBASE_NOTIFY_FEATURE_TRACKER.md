# Firebase Cloud Function /notify Feature Tracker

## Project Overview
**Goal**: Complete the Firebase Cloud Function push-notification feature for train location-based notifications.

**Current Status**: Client-side infrastructure complete, backend Cloud Functions missing.

---

## 1 · Already Implemented ✅

### Client-Side Components
- [x] **FcmTokenService** - `lib/services/fcm_token_service.dart`
  - Complete FCM token management with server sync
  
- [x] **FirebaseMessagingService** - `lib/services/firebase_messaging_service.dart`
  - Handles foreground/background FCM messages
  
- [x] **OnboardingNotificationService** - `lib/services/notification_services/onboarding_notification_service.dart`
  - Comprehensive notification processing logic
  
- [x] **NotificationTestingScreen** - `lib/screens/notification_testing/notification_testing_screen.dart`
  - Manual testing interface with real coordinates
  
- [x] **OnboardingResponse Model** - `lib/types/attendance_types/onboarding_response.dart`
  - Model for /microservice/train/location/ API response
  
- [x] **LocationService** - `lib/services/attendance_services/location_service.dart`
  - Fetches train location data from microservice endpoint
  
- [x] **NotificationPreferencesModel** - `lib/models/notification_preferences_model.dart`
  - User notification preferences with sound/vibration settings

### Platform Configuration
- [x] **Android notification channel** - `android/app/src/main/AndroidManifest.xml`
  - "high_importance_channel" configured
  
- [x] **Android notification icon** - `android/app/src/main/res/drawable/ic_notification.xml`
  - Custom notification icon
  
- [x] **Android notification color** - `android/app/src/main/res/values/colors.xml`
  - Green notification color (#4CAF50)
  
- [x] **iOS Firebase setup** - `ios/Runner/AppDelegate.swift`
  - Firebase configured with notification permissions
  
- [x] **Firebase project config** - Android/iOS google-services files
  - railwaysapp-prod project configured

---

## 2 · TODO (Missing Implementation) ❌

### 1. Firebase Cloud Functions Infrastructure ✅
- [x] **Task**: Create Firebase Functions directory structure and POST /notify endpoint
- **File**: `functions/src/index.ts`
- **Priority**: HIGH
- **Status**: COMPLETED - Full implementation with train location fetching, coach table building, and FCM sending
- **Implementation**:
  - Complete TypeScript Cloud Function with error handling
  - Fetches data from `/microservice/train/location/` API
  - Builds formatted coach tables with passenger counts
  - Sends FCM notifications with custom sound support
  - Includes CORS support and comprehensive logging

### 2. Firebase Functions Package Configuration ✅
- [x] **Task**: Initialize Firebase Functions with required dependencies
- **File**: `functions/package.json`
- **Priority**: HIGH
- **Status**: COMPLETED - Full package configuration with dev dependencies
- **Implementation**:
  - Node.js 18 runtime configuration
  - Firebase Admin SDK ^12.0.0 and Functions ^4.0.0
  - TypeScript, ESLint, Jest testing setup
  - Build, lint, test, and deployment scripts

### 3. RAILOPS_BEARER Secret Configuration ✅
- [x] **Task**: Store API bearer token as Firebase Functions secret
- **Command**: `firebase functions:secrets:set RAILOPS_BEARER`
- **Priority**: HIGH
- **Status**: COMPLETED - Secret handling implemented in code
- **Implementation**: Function reads secret via `functions.config().railops?.bearer`

### 4. Firestore Token Storage Schema ✅
- [x] **Task**: Implement Firestore collection structure for FCM tokens
- **File**: `functions/src/index.ts` (token lookup logic)
- **Priority**: HIGH
- **Status**: COMPLETED - Full token lookup implementation
- **Implementation**:
  - `getUserFcmToken()` function for token retrieval
  - Reads from `tokens/{user_id}` collection
  - Error handling for missing tokens
  - Firestore security rules configured

### 5. Firestore Anti-Duplication Schema ✅
- [x] **Task**: Implement sentAlerts collection to prevent duplicate notifications
- **File**: `functions/src/index.ts` (anti-dup logic)
- **Priority**: MEDIUM
- **Status**: COMPLETED - Full anti-duplication system
- **Implementation**:
  - `isAlertAlreadySent()` and `markAlertAsSent()` functions
  - Alert key format: `{user_id}/{date}/{train_number}`
  - Firestore `sentAlerts` collection with timestamps
  - Prevents duplicate notifications per user/train/date

### 6. Custom Notification Sound Assets
- [ ] **Task**: Add railops_alarm sound files for Android and iOS
- **Files**: 
  - `android/app/src/main/res/raw/railops_alarm.mp3`
  - `ios/Runner/railops_alarm.caf`
- **Priority**: MEDIUM

### 7. RailOps Alerts Notification Channel
- [ ] **Task**: Create dedicated notification channel for train alerts
- **File**: `lib/services/firebase_messaging_service.dart` (modify setupFlutterNotifications)
- **Priority**: MEDIUM
- **Code Stub**:
```dart
await _flutterLocalNotificationsPlugin
  .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>()
  ?.createNotificationChannel(const AndroidNotificationChannel(
    'railops_alerts', 'RailOps Train Alerts',
    description: 'Critical train boarding/off-boarding notifications',
    importance: Importance.max,
    sound: RawResourceAndroidNotificationSound('railops_alarm'),
  ));
```

### 8. Snooze Functionality Implementation
- [ ] **Task**: Add 10-minute snooze action handling in Flutter notification system
- **File**: `lib/services/firebase_messaging_service.dart`
- **Priority**: LOW
- **Code Stub**:
```dart
const AndroidNotificationDetails(
  actions: [
    AndroidNotificationAction('snooze_10min', 'Snooze 10 min'),
    AndroidNotificationAction('dismiss', 'Dismiss'),
  ],
);
```

### 9. Firebase Functions Deployment Configuration ✅
- [x] **Task**: Create Firebase configuration for Functions deployment
- **File**: `firebase.json`
- **Priority**: HIGH
- **Status**: COMPLETED - Full Firebase project configuration
- **Implementation**:
  - Node.js 18 runtime configuration
  - Pre-deploy linting and build steps
  - Firestore rules and indexes configuration
  - Local emulator setup for development
  - Complete project structure for independent deployment

### 10. API Documentation and Testing ✅
- [x] **Task**: Create README with curl examples for backend integration
- **File**: `functions/README.md`
- **Priority**: LOW
- **Status**: COMPLETED - Comprehensive documentation with deployment automation
- **Implementation**:
  - Complete API documentation with request/response examples
  - Automated deployment script `deploy_notify.sh`
  - Testing section with local and production curl examples
  - First-time deployment instructions

### 11. Deployment Automation ✅
- [x] **Task**: Create automated deployment script for RAILOPS_BEARER secret and function deployment
- **File**: `functions/deploy_notify.sh`
- **Priority**: HIGH
- **Status**: COMPLETED - One-command deployment solution
- **Implementation**:
  - Single script handles secret storage and deployment
  - Usage: `./deploy_notify.sh "<RAILOPS_BEARER_TOKEN>"`
  - Provides endpoint URL and test curl command
  - Executable permissions configured

---

## Implementation Priority Order

### Phase 1: Core Backend (HIGH Priority) ✅ COMPLETED
1. ✅ Firebase Functions Infrastructure (#1)
2. ✅ Package Configuration (#2)
3. ✅ Secret Configuration (#3)
4. ✅ Token Storage Schema (#4)
5. ✅ Deployment Configuration (#9)

### Phase 2: Notification Enhancement (MEDIUM Priority)
6. ✅ Anti-Duplication Schema (#5)
7. Custom Sound Assets (#6)
8. RailOps Alerts Channel (#7)

### Phase 3: Polish & Documentation (LOW Priority)
9. Snooze Functionality (#8)
10. ✅ API Documentation (#10)

---

## Notes
- Firebase project: `railwaysapp-prod` (ID: 513557807469)
- Package name: `com.biputri.railops`
- Train location API: `/microservice/train/location/`
- 50km radius filtering handled by backend
- Coach table format: StationCode | Coach | Onboarding/Off-boarding/Vacant counts

---

**Last Updated**: December 2024
**Status**: Phase 1 COMPLETED ✅ - Core backend infrastructure ready for deployment

## Phase 1 Implementation Summary ✅

**Branch**: `feature/firebase-cloud-functions`

**Completed Components**:
- ✅ Complete Firebase Functions directory structure (`functions/`)
- ✅ TypeScript configuration with ESLint and Jest
- ✅ POST `/notify` endpoint with full implementation
- ✅ Train location API integration (`/microservice/train/location/`)
- ✅ Coach table building with passenger counts
- ✅ FCM notification sending with custom sound support
- ✅ Firestore token lookup from `tokens/{user_id}` collection
- ✅ Anti-duplication system using `sentAlerts` collection
- ✅ RAILOPS_BEARER secret configuration
- ✅ Firebase project configuration (`firebase.json`)
- ✅ Firestore security rules and indexes
- ✅ Comprehensive documentation and testing setup

**Next Steps**:
1. ✅ Automated deployment script created: `functions/deploy_notify.sh`
2. **Ready for deployment**: `cd functions && ./deploy_notify.sh "<RAILOPS_BEARER_TOKEN>"`
3. Test endpoint with provided curl command
4. Proceed to Phase 2 (notification enhancements)
